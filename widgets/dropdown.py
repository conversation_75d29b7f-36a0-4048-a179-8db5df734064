from fabric.widgets.box import Box
from fabric.widgets.centerbox import CenterBox
from fabric.widgets.eventbox import EventBox

from utils.service import modus_service
from widgets.popup_window import PopupWindow

dropdowns = []


def dropdown_divider(comment):
    return Box(
        children=[Box(name="dropdown-divider", h_expand=True)],
        name="dropdown-divider-box",
        h_align="fill",
        h_expand=True,
        v_expand=True,
    )


class ModusDropdown(PopupWindow):
    def __init__(self, dropdown_children=None, dropdown_id=None, **kwargs):
        super().__init__(
            layer="top",
            exclusivity="auto",
            name="dropdown-menu",
            keyboard_mode="none",
            visible=False,
            **kwargs,
        )

        self.id = dropdown_id or str(len(dropdowns))
        dropdowns.append(self)

        modus_service.connect("dropdowns-hide-changed", self.hide_dropdown)

        self.dropdown = Box(
            children=dropdown_children or [],
            h_expand=True,
            name="dropdown-options",
            orientation="vertical",
        )

        self.child_box = CenterBox(start_children=[self.dropdown])

        self.event_box = EventBox(
            events=["enter-notify-event", "leave-notify-event"],
            child=self.child_box,
            all_visible=True,
        )

        self.children = [self.event_box]
        # Temporarily remove button-press-event to test if it's interfering
        # self.connect("button-press-event", self.hide_dropdown)
        self.add_keybinding("Escape", self.hide_dropdown)

    def toggle_dropdown(self, button, parent=None):
        self.set_visible(not self.is_visible())
        modus_service.current_dropdown = self.id if self.is_visible() else None

    def hide_dropdown(self, widget, event):
        print(f"DEBUG: hide_dropdown called for {self.id}")
        # Check if the event target is a button - if so, let the button handle it
        if hasattr(event, 'widget'):
            target = event.widget
            print(f"DEBUG: Event widget: {target}")
            # Walk up the widget tree to see if we clicked on a button
            while target:
                if hasattr(target, 'get_name'):
                    widget_name = target.get_name()
                    print(f"DEBUG: Checking widget: {widget_name}")
                    if widget_name == "dropdown-option":
                        print(f"DEBUG: Found dropdown-option button, not hiding dropdown")
                        # This is a button click, don't hide the dropdown here
                        # The button's click handler will hide it after executing the command
                        return False
                target = getattr(target, 'get_parent', lambda: None)()

        if str(modus_service.current_dropdown) != str(self.id) and self.is_visible():
            print(f"DEBUG: Hiding dropdown {self.id}")
            self.hide()
        return False

    def _set_mousecapture(self, visible: bool) -> None:
        self.set_visible(visible)
        if visible:
            modus_service.current_dropdown = self.id

    def on_cursor_enter(self, *_):
        self.set_visible(True)

    def on_cursor_leave(self, *_):
        if self.is_hovered():
            return
        self.set_visible(False)
        modus_service.dropdowns_hide = not modus_service.dropdowns_hide
