from typing import Any

from gi.repository import GtkLayerShell  # type: ignore

from fabric.widgets.eventbox import EventBox
from widgets.wayland import WaylandWindow as Window
from fabric.widgets.widget import Widget
from utils.service import modus_service


class MouseCapture(Window):
    """A Window that spans across the entire screen, to essentially catch the mouse"""

    def __init__(self, layer: str, child_window: Window, **kwargs):
        super().__init__(
            layer=layer,
            anchor="top bottom left right",
            exclusivity="auto",
            title="modus-mousecatch",
            name="MouseCapture",
            keyboard_mode="exclusive",
            all_visible=False,
            visible=False,
            **kwargs,
        )

        GtkLayerShell.set_exclusive_zone(self, -1)

        self.child_window = child_window

        if hasattr(self.child_window, "_init_mousecapture"):
            self.child_window._init_mousecapture(self)

        # when clicking on the mousecapture, hide the child window
        self.event_box = EventBox(
            events=[
                "enter-notify-event",
                "leave-notify-event",
                "button-press-event",
                "key-press-event",
            ],
            all_visible=True,
        )
        self.event_box.connect("button-press-event", self.on_button_press)
        self.add_keybinding("Escape", self.hide_child_window)

        self.children = [self.event_box]

    def on_button_press(self, widget: Widget, event: Any) -> bool:
        """Handle button press events, allowing dropdown option buttons to work."""
        print(f"DEBUG: MouseCapture on_button_press called")

        # Check if the click is on a dropdown option button
        if hasattr(event, 'widget'):
            target = event.widget
            print(f"DEBUG: MouseCapture event widget: {target}")
            # Walk up the widget tree to see if we clicked on a dropdown option
            while target:
                if hasattr(target, 'get_name'):
                    widget_name = target.get_name()
                    print(f"DEBUG: MouseCapture checking widget: {widget_name}")
                    if widget_name == "dropdown-option":
                        print(f"DEBUG: MouseCapture found dropdown-option, allowing event to propagate")
                        # Return False to allow the event to propagate to the button
                        return False
                target = getattr(target, 'get_parent', lambda: None)()

        # If we get here, it's not a dropdown option click, so hide the window
        print(f"DEBUG: MouseCapture hiding child window")
        self.hide_child_window(widget, event)
        return True  # Consume the event

    def show_child_window(self, widget: Widget, event: Any) -> None:
        self.set_child_window_visible(True)

    def hide_child_window(self, widget: Widget, event: Any) -> None:
        self.set_child_window_visible(False)

    def set_child_window_visible(self, visible: bool) -> None:
        self.child_window._set_mousecapture(visible)
        self.set_visible(visible)
        self.pass_through = not visible

    def toggle_mousecapture(self, *_) -> None:
        self.set_visible(not self.is_visible())
        self.keyboard_mode = "none" if self.is_visible() else "exclusive"
        if self.is_visible():
            self.steal_input()
        else:
            self.return_input()
        self.pass_through = not self.is_visible()
        self.child_window._set_mousecapture(self.is_visible())


class DropDownMouseCapture(MouseCapture):
    """A Window that spans across the entire screen, to essentially catch the mouse"""

    def __init__(self, *args, **kwargs):
        super().__init__(
            *args,
            **kwargs,
        )
        modus_service.connect("dropdowns-hide-changed", self.dropdowns_hide_changed)

    def hide_child_window(self, widget: Widget, event: Any) -> None:
        print(f"DEBUG: DropDownMouseCapture hide_child_window called")
        self.child_window._set_mousecapture(False)
        modus_service.current_dropdown = None
        self.set_visible(False)
        self.pass_through = True

    def dropdowns_hide_changed(self, widget: Widget, event: Any) -> None:
        if modus_service.current_dropdown == self.child_window.id:
            return
        return super().hide_child_window(widget, event)
